import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";
import { UnifiedFormSubmission, ContactFormSubmissionStatus } from "@/types/contact";

// GET /api/admin/form-submissions - Get all form submissions (contact + event)
export async function GET(request: NextRequest) {
    try {
        // Use service role for admin operations to bypass RLS
        const supabase = await createClient(true);

        const { searchParams } = new URL(request.url);
        const page = parseInt(searchParams.get('page') || '1');
        const limit = parseInt(searchParams.get('limit') || '20');
        const status = searchParams.get('status') as ContactFormSubmissionStatus | 'all';
        const formType = searchParams.get('form_type') as 'contact' | 'event' | 'all';
        const search = searchParams.get('search') || '';
        const isSpam = searchParams.get('is_spam');

        const offset = (page - 1) * limit;

        // Fetch contact form submissions
        let contactQuery = supabase
            .from('contact_form_submissions')
            .select('*');

        // Fetch event form submissions with event details
        let eventQuery = supabase
            .from('event_form_submissions')
            .select(`
                *,
                event:events(
                    id,
                    title,
                    slug
                )
            `);

        // Apply filters
        if (status && status !== 'all') {
            contactQuery = contactQuery.eq('status', status);
            eventQuery = eventQuery.eq('status', status);
        }

        if (isSpam !== null) {
            const spamFilter = isSpam === 'true';
            contactQuery = contactQuery.eq('is_spam', spamFilter);
            eventQuery = eventQuery.eq('is_spam', spamFilter);
        }

        if (search) {
            const searchFilter = `name.ilike.%${search}%,email.ilike.%${search}%,company_name.ilike.%${search}%,exhibition_name.ilike.%${search}%,message.ilike.%${search}%`;
            contactQuery = contactQuery.or(searchFilter);
            eventQuery = eventQuery.or(searchFilter);
        }

        // Execute queries based on form type filter
        let contactSubmissions: any[] = [];
        let eventSubmissions: any[] = [];

        if (formType === 'all' || formType === 'contact') {
            const { data: contactData, error: contactError } = await contactQuery;
            if (contactError) {
                console.error('Error fetching contact submissions:', contactError);
            } else {
                contactSubmissions = contactData || [];
            }
        }

        if (formType === 'all' || formType === 'event') {
            const { data: eventData, error: eventError } = await eventQuery;
            if (eventError) {
                console.error('Error fetching event submissions:', eventError);
            } else {
                eventSubmissions = eventData || [];
            }
        }

        // Transform and unify the submissions
        const unifiedSubmissions: UnifiedFormSubmission[] = [
            // Contact form submissions
            ...contactSubmissions.map((submission: any) => ({
                ...submission,
                form_type: 'contact' as const,
                // Ensure all fields are present
                budget: undefined,
                event_id: undefined,
                event: undefined,
                agreed_to_terms: submission.agreed_to_terms || false,
            })),
            // Event form submissions
            ...eventSubmissions.map((submission: any) => ({
                ...submission,
                form_type: 'event' as const,
                // Map event form fields to unified structure
                company_name: submission.company_name || submission.company,
                // Ensure all fields are present
                agreed_to_terms: undefined,
                attachment_type: undefined, // Event forms don't track MIME type
            }))
        ];

        // Sort by created_at descending
        unifiedSubmissions.sort((a, b) => 
            new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        );

        // Apply pagination
        const totalSubmissions = unifiedSubmissions.length;
        const paginatedSubmissions = unifiedSubmissions.slice(offset, offset + limit);
        const totalPages = Math.ceil(totalSubmissions / limit);

        return NextResponse.json({
            success: true,
            data: {
                submissions: paginatedSubmissions,
                pagination: {
                    page,
                    limit,
                    total: totalSubmissions,
                    totalPages,
                    hasNext: page < totalPages,
                    hasPrev: page > 1
                }
            }
        });

    } catch (error) {
        console.error('Error fetching form submissions:', error);
        return NextResponse.json(
            { 
                success: false, 
                error: 'Failed to fetch form submissions' 
            },
            { status: 500 }
        );
    }
}

// PATCH /api/admin/form-submissions - Update submission status
export async function PATCH(request: NextRequest) {
    try {
        const supabase = await createClient(true);
        const { id, form_type, status, admin_notes, is_spam } = await request.json();

        if (!id || !form_type) {
            return NextResponse.json(
                { success: false, error: 'ID and form_type are required' },
                { status: 400 }
            );
        }

        const tableName = form_type === 'contact' 
            ? 'contact_form_submissions' 
            : 'event_form_submissions';

        const updateData: any = {
            updated_at: new Date().toISOString()
        };

        if (status) updateData.status = status;
        if (admin_notes !== undefined) updateData.admin_notes = admin_notes;
        if (is_spam !== undefined) updateData.is_spam = is_spam;

        const { error } = await supabase
            .from(tableName)
            .update(updateData)
            .eq('id', id);

        if (error) {
            console.error('Error updating submission:', error);
            return NextResponse.json(
                { success: false, error: 'Failed to update submission' },
                { status: 500 }
            );
        }

        return NextResponse.json({ success: true });

    } catch (error) {
        console.error('Error updating submission:', error);
        return NextResponse.json(
            { success: false, error: 'Failed to update submission' },
            { status: 500 }
        );
    }
}
