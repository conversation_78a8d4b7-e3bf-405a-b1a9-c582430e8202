import { UnifiedFormSubmission, ContactFormSubmissionStatus, FormSubmissionType } from '@/types/contact';

export interface UnifiedFormSubmissionQueryParams {
    page?: number;
    limit?: number;
    status?: ContactFormSubmissionStatus | 'all';
    form_type?: FormSubmissionType | 'all';
    search?: string;
    is_spam?: boolean;
}

export interface UnifiedFormSubmissionsResponse {
    success: boolean;
    data?: {
        submissions: UnifiedFormSubmission[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
            hasNext: boolean;
            hasPrev: boolean;
        };
    };
    error?: string;
}

export interface UnifiedFormSubmissionUpdateData {
    id: string;
    form_type: FormSubmissionType;
    status?: ContactFormSubmissionStatus;
    admin_notes?: string;
    is_spam?: boolean;
}

class UnifiedFormSubmissionsService {
    private baseUrl = '/api/admin/form-submissions';

    // Fetch all form submissions (contact + event)
    async getSubmissions(params: UnifiedFormSubmissionQueryParams = {}): Promise<UnifiedFormSubmissionsResponse> {
        try {
            const searchParams = new URLSearchParams();
            
            if (params.page) searchParams.set('page', params.page.toString());
            if (params.limit) searchParams.set('limit', params.limit.toString());
            if (params.status) searchParams.set('status', params.status);
            if (params.form_type) searchParams.set('form_type', params.form_type);
            if (params.search) searchParams.set('search', params.search);
            if (params.is_spam !== undefined) searchParams.set('is_spam', params.is_spam.toString());

            const response = await fetch(`${this.baseUrl}?${searchParams.toString()}`);
            const result = await response.json();

            if (!response.ok) {
                throw new Error(result.error || 'Failed to fetch submissions');
            }

            return result;
        } catch (error) {
            console.error('Error fetching submissions:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to fetch submissions'
            };
        }
    }

    // Update submission status
    async updateSubmission(updateData: UnifiedFormSubmissionUpdateData): Promise<boolean> {
        try {
            const response = await fetch(this.baseUrl, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(updateData),
            });

            const result = await response.json();

            if (!response.ok) {
                throw new Error(result.error || 'Failed to update submission');
            }

            return result.success;
        } catch (error) {
            console.error('Error updating submission:', error);
            return false;
        }
    }

    // Update submission status (convenience method)
    async updateSubmissionStatus(
        id: string, 
        formType: FormSubmissionType, 
        status: ContactFormSubmissionStatus
    ): Promise<boolean> {
        return this.updateSubmission({ id, form_type: formType, status });
    }

    // Mark submission as spam/not spam
    async updateSpamStatus(
        id: string, 
        formType: FormSubmissionType, 
        isSpam: boolean
    ): Promise<boolean> {
        return this.updateSubmission({ 
            id, 
            form_type: formType, 
            is_spam: isSpam,
            status: isSpam ? 'spam' : 'new'
        });
    }

    // Add admin notes
    async addAdminNotes(
        id: string, 
        formType: FormSubmissionType, 
        notes: string
    ): Promise<boolean> {
        return this.updateSubmission({ id, form_type: formType, admin_notes: notes });
    }

    // Send reply (updates status to replied and adds admin notes)
    async sendReply(
        id: string, 
        formType: FormSubmissionType, 
        replyMessage: string
    ): Promise<boolean> {
        return this.updateSubmission({ 
            id, 
            form_type: formType, 
            status: 'replied',
            admin_notes: replyMessage
        });
    }

    // Get submission statistics
    async getSubmissionStats(): Promise<{
        total: number;
        new: number;
        read: number;
        replied: number;
        spam: number;
        contact_forms: number;
        event_forms: number;
    }> {
        try {
            const response = await this.getSubmissions({ limit: 1000 }); // Get all for stats
            
            if (!response.success || !response.data) {
                return {
                    total: 0,
                    new: 0,
                    read: 0,
                    replied: 0,
                    spam: 0,
                    contact_forms: 0,
                    event_forms: 0
                };
            }

            const submissions = response.data.submissions;
            
            return {
                total: submissions.length,
                new: submissions.filter(s => s.status === 'new').length,
                read: submissions.filter(s => s.status === 'read').length,
                replied: submissions.filter(s => s.status === 'replied').length,
                spam: submissions.filter(s => s.is_spam || s.status === 'spam').length,
                contact_forms: submissions.filter(s => s.form_type === 'contact').length,
                event_forms: submissions.filter(s => s.form_type === 'event').length,
            };
        } catch (error) {
            console.error('Error getting submission stats:', error);
            return {
                total: 0,
                new: 0,
                read: 0,
                replied: 0,
                spam: 0,
                contact_forms: 0,
                event_forms: 0
            };
        }
    }
}

// Export singleton instance
export const unifiedFormSubmissionsService = new UnifiedFormSubmissionsService();
export default unifiedFormSubmissionsService;
