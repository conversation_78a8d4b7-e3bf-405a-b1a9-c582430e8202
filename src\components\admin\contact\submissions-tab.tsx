"use client";

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { contactAdminService } from '@/lib/services/contact';
import { UnifiedFormSubmission, ContactFormSubmissionStatus, FormSubmissionType } from '@/types/contact';
import { unifiedFormSubmissionsService } from '@/services/unified-form-submissions.service';
import { 
    Search, 
    Filter, 
    Eye, 
    Reply, 
    Trash2, 
    Download,
    Loader2, 
    AlertCircle, 
    CheckCircle,
    Mail,
    Phone,
    Calendar,
    User,
    MessageSquare,
    Archive,
    Star,
    FileText,
    Users
} from 'lucide-react';

interface ContactSubmissionsTabProps {
    onStatsUpdate?: () => void;
}

export default function ContactSubmissionsTab({ onStatsUpdate }: ContactSubmissionsTabProps) {
    const [submissions, setSubmissions] = useState<UnifiedFormSubmission[]>([]);
    const [loading, setLoading] = useState(true);
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState<ContactFormSubmissionStatus | 'all'>('all');
    const [formTypeFilter, setFormTypeFilter] = useState<FormSubmissionType | 'all'>('all');
    const [selectedSubmission, setSelectedSubmission] = useState<UnifiedFormSubmission | null>(null);
    const [showViewDialog, setShowViewDialog] = useState(false);
    const [showReplyDialog, setShowReplyDialog] = useState(false);
    const [replyMessage, setReplyMessage] = useState('');
    const [error, setError] = useState<string>('');
    const [success, setSuccess] = useState<string>('');
    const [actionLoading, setActionLoading] = useState(false);

    useEffect(() => {
        loadSubmissions();
    }, []);

    const loadSubmissions = async () => {
        try {
            setLoading(true);
            const response = await unifiedFormSubmissionsService.getSubmissions({
                limit: 100, // Get more submissions for better filtering
                status: statusFilter === 'all' ? undefined : statusFilter,
                form_type: formTypeFilter === 'all' ? undefined : formTypeFilter,
                search: searchTerm || undefined
            });

            if (response.success && response.data) {
                console.log('Loaded unified submissions:', response.data.submissions);
                setSubmissions(response.data.submissions);
            } else {
                throw new Error(response.error || 'Failed to load submissions');
            }
        } catch (error) {
            console.error('Failed to load submissions:', error);
            setError('Failed to load submissions');
        } finally {
            setLoading(false);
        }
    };

    const filteredSubmissions = submissions.filter(submission => {
        const matchesSearch = !searchTerm ||
            submission.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            submission.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
            (submission.company_name && submission.company_name.toLowerCase().includes(searchTerm.toLowerCase())) ||
            (submission.exhibition_name && submission.exhibition_name.toLowerCase().includes(searchTerm.toLowerCase())) ||
            (submission.message && submission.message.toLowerCase().includes(searchTerm.toLowerCase())) ||
            (submission.budget && submission.budget.toLowerCase().includes(searchTerm.toLowerCase()));

        const matchesStatus = statusFilter === 'all' || submission.status === statusFilter;
        const matchesFormType = formTypeFilter === 'all' || submission.form_type === formTypeFilter;

        return matchesSearch && matchesStatus && matchesFormType;
    });

    // Debug logging
    console.log('Total submissions:', submissions.length);
    console.log('Filtered submissions:', filteredSubmissions.length);
    console.log('Search term:', searchTerm);
    console.log('Status filter:', statusFilter);
    console.log('Form type filter:', formTypeFilter);

    // Count form types
    const contactForms = submissions.filter(s => s.form_type === 'contact').length;
    const eventForms = submissions.filter(s => s.form_type === 'event').length;
    console.log('Contact forms:', contactForms);
    console.log('Event forms:', eventForms);

    const handleViewSubmission = (submission: UnifiedFormSubmission) => {
        setSelectedSubmission(submission);
        setShowViewDialog(true);
        // Mark as read if it's new
        if (submission.status === 'new') {
            handleStatusChange(submission.id, submission.form_type, 'read');
        }
    };

    const handleReplySubmission = (submission: UnifiedFormSubmission) => {
        setSelectedSubmission(submission);
        setReplyMessage('');
        setShowReplyDialog(true);
    };

    // Download attachment handler
    const handleDownloadAttachment = async (submission: UnifiedFormSubmission) => {
        if (!submission.attachment_url) return;

        try {
            // Create a temporary link to download the file
            const link = document.createElement('a');
            link.href = submission.attachment_url;
            link.download = submission.attachment_filename || 'attachment';
            link.target = '_blank';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        } catch (error) {
            console.error('Error downloading attachment:', error);
        }
    };

    const handleStatusChange = async (submissionId: string, formType: FormSubmissionType, newStatus: ContactFormSubmissionStatus) => {
        try {
            setActionLoading(true);
            const success = await unifiedFormSubmissionsService.updateSubmissionStatus(submissionId, formType, newStatus);
            if (success) {
                setSubmissions(prev =>
                    prev.map(sub =>
                        sub.id === submissionId
                            ? { ...sub, status: newStatus, updated_at: new Date().toISOString() }
                            : sub
                    )
                );
                setSuccess('Status updated successfully');
                onStatsUpdate?.();
            } else {
                setError('Failed to update status');
            }
        } catch (error) {
            console.error('Status update error:', error);
            setError('Failed to update status');
        } finally {
            setActionLoading(false);
        }
    };

    const handleDeleteSubmission = async (submissionId: string) => {
        if (!confirm('Are you sure you want to delete this submission? This action cannot be undone.')) {
            return;
        }

        try {
            setActionLoading(true);
            const success = await contactAdminService.deleteSubmission(submissionId);
            if (success) {
                setSubmissions(prev => prev.filter(sub => sub.id !== submissionId));
                setSuccess('Submission deleted successfully');
                onStatsUpdate?.();
            } else {
                setError('Failed to delete submission');
            }
        } catch (error) {
            console.error('Delete error:', error);
            setError('Failed to delete submission');
        } finally {
            setActionLoading(false);
        }
    };

    const handleSendReply = async () => {
        if (!selectedSubmission || !replyMessage.trim()) {
            setError('Reply message is required');
            return;
        }

        try {
            setActionLoading(true);
            const success = await unifiedFormSubmissionsService.sendReply(
                selectedSubmission.id,
                selectedSubmission.form_type,
                replyMessage
            );
            if (success) {
                setSuccess('Reply sent successfully');
                setShowReplyDialog(false);
                setReplyMessage('');
                // Update local state
                setSubmissions(prev =>
                    prev.map(sub =>
                        sub.id === selectedSubmission.id
                            ? { ...sub, status: 'replied' as ContactFormSubmissionStatus, admin_notes: replyMessage, updated_at: new Date().toISOString() }
                            : sub
                    )
                );
                onStatsUpdate?.();
            } else {
                setError('Failed to send reply');
            }
        } catch (error) {
            console.error('Reply error:', error);
            setError('Failed to send reply');
        } finally {
            setActionLoading(false);
        }
    };

    const getStatusBadge = (status: ContactFormSubmissionStatus) => {
        const variants = {
            new: 'bg-blue-100 text-blue-800',
            read: 'bg-gray-100 text-gray-800',
            replied: 'bg-green-100 text-green-800',
            archived: 'bg-yellow-100 text-yellow-800',
            spam: 'bg-red-100 text-red-800'
        };

        const labels = {
            new: 'New',
            read: 'Read',
            replied: 'Replied',
            archived: 'Archived',
            spam: 'Spam'
        };

        return (
            <Badge className={variants[status]}>
                {labels[status]}
            </Badge>
        );
    };

    const formatDate = (dateString: string | undefined) => {
        if (!dateString) return 'N/A';
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    if (loading) {
        return (
            <div className="flex items-center justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin" />
                <span className="ml-2">Loading submissions...</span>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {error && (
                <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>{error}</AlertDescription>
                </Alert>
            )}

            {success && (
                <Alert className="border-green-200 bg-green-50">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <AlertDescription className="text-green-800">{success}</AlertDescription>
                </Alert>
            )}

            {/* Filters */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <MessageSquare className="h-5 w-5" />
                        Form Submissions
                    </CardTitle>
                    <CardDescription>
                        View and manage all contact form and booth requirement submissions
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <div className="flex flex-col sm:flex-row gap-4">
                        <div className="flex-1">
                            <Label htmlFor="search">Search Submissions</Label>
                            <div className="relative mt-1">
                                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                                <Input
                                    id="search"
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    placeholder="Search by name, email, company, exhibition, or message..."
                                    className="pl-10"
                                />
                            </div>
                        </div>
                        <div className="sm:w-48">
                            <Label htmlFor="status-filter">Filter by Status</Label>
                            <Select value={statusFilter} onValueChange={(value: any) => setStatusFilter(value)}>
                                <SelectTrigger className="mt-1">
                                    <SelectValue placeholder="All statuses" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Statuses</SelectItem>
                                    <SelectItem value="new">New</SelectItem>
                                    <SelectItem value="read">Read</SelectItem>
                                    <SelectItem value="replied">Replied</SelectItem>
                                    <SelectItem value="archived">Archived</SelectItem>
                                    <SelectItem value="spam">Spam</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                        <div className="sm:w-48">
                            <Label htmlFor="form-type-filter">Filter by Form Type</Label>
                            <Select value={formTypeFilter} onValueChange={(value: any) => setFormTypeFilter(value)}>
                                <SelectTrigger className="mt-1">
                                    <SelectValue placeholder="All forms" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Forms</SelectItem>
                                    <SelectItem value="contact">Contact Forms</SelectItem>
                                    <SelectItem value="event">Event Forms</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                    </div>
                </CardContent>
            </Card>

            {/* Submissions List */}
            <div className="space-y-4">
                {filteredSubmissions.length === 0 ? (
                    <Card>
                        <CardContent className="text-center py-8">
                            <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                            <h3 className="text-lg font-semibold text-gray-900 mb-2">
                                {searchTerm || statusFilter !== 'all' ? 'No Matching Submissions' : 'No Submissions Yet'}
                            </h3>
                            <p className="text-gray-600">
                                {searchTerm || statusFilter !== 'all' 
                                    ? 'Try adjusting your search or filter criteria.'
                                    : 'Contact form submissions will appear here when users submit the form.'
                                }
                            </p>
                        </CardContent>
                    </Card>
                ) : (
                    filteredSubmissions.map((submission) => (
                        <Card key={submission.id} className={`${submission.status === 'new' ? 'border-blue-200 bg-blue-50/30' : ''}`}>
                            <CardContent className="p-6">
                                <div className="flex justify-between items-start mb-4">
                                    <div className="flex-1">
                                        <div className="flex items-center gap-3 mb-2">
                                            <h3 className="font-semibold text-lg">
                                                {submission.exhibition_name
                                                    ? `${submission.name} - ${submission.exhibition_name}`
                                                    : submission.company_name
                                                        ? `${submission.name} - ${submission.company_name}`
                                                        : submission.name
                                                }
                                            </h3>
                                            {getStatusBadge(submission.status)}
                                            {/* Form type indicator */}
                                            <Badge
                                                variant="secondary"
                                                className={submission.form_type === 'event'
                                                    ? "bg-blue-100 text-blue-800 border-blue-200"
                                                    : "bg-green-100 text-green-800 border-green-200"
                                                }
                                            >
                                                {submission.form_type === 'event' ? 'Event Form' : 'Contact Form'}
                                            </Badge>
                                            {submission.event && (
                                                <Badge variant="outline" className="text-xs">
                                                    {submission.event.title}
                                                </Badge>
                                            )}
                                            {submission.status === 'new' && <Star className="h-4 w-4 text-blue-500" />}
                                        </div>
                                        <div className="flex flex-wrap gap-4 text-sm text-gray-600 mb-3">
                                            <div className="flex items-center gap-1">
                                                <User className="h-4 w-4" />
                                                <span>{submission.name}</span>
                                            </div>
                                            <div className="flex items-center gap-1">
                                                <Mail className="h-4 w-4" />
                                                <span>{submission.email}</span>
                                            </div>
                                            {submission.phone && (
                                                <div className="flex items-center gap-1">
                                                    <Phone className="h-4 w-4" />
                                                    <span>{submission.phone}</span>
                                                </div>
                                            )}
                                            <div className="flex items-center gap-1">
                                                <Calendar className="h-4 w-4" />
                                                <span>{formatDate(submission.created_at)}</span>
                                            </div>
                                        </div>
                                        <p className="text-gray-700 line-clamp-2">{submission.message}</p>
                                    </div>
                                    <div className="flex gap-2 ml-4">
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => handleViewSubmission(submission)}
                                            className="flex items-center gap-1"
                                        >
                                            <Eye className="h-4 w-4" />
                                            View
                                        </Button>
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => handleReplySubmission(submission)}
                                            className="flex items-center gap-1"
                                        >
                                            <Reply className="h-4 w-4" />
                                            Reply
                                        </Button>
                                        <Select
                                            value={submission.status}
                                            onValueChange={(value: ContactFormSubmissionStatus) =>
                                                handleStatusChange(submission.id, submission.form_type, value)
                                            }
                                        >
                                            <SelectTrigger className="w-32">
                                                <SelectValue />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="new">New</SelectItem>
                                                <SelectItem value="read">Read</SelectItem>
                                                <SelectItem value="replied">Replied</SelectItem>
                                                <SelectItem value="archived">Archived</SelectItem>
                                                <SelectItem value="spam">Spam</SelectItem>
                                            </SelectContent>
                                        </Select>
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => handleDeleteSubmission(submission.id)}
                                            className="text-red-600 hover:text-red-700"
                                        >
                                            <Trash2 className="h-4 w-4" />
                                        </Button>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    ))
                )}
            </div>

            {/* View Submission Dialog */}
            <Dialog open={showViewDialog} onOpenChange={setShowViewDialog}>
                <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
                    <DialogHeader>
                        <DialogTitle>Submission Details</DialogTitle>
                        <DialogDescription>
                            Complete form submission information with metadata
                        </DialogDescription>
                    </DialogHeader>
                    
                    {selectedSubmission && (
                        <div className="space-y-6 max-h-[70vh] overflow-y-auto">
                            {/* Basic Information */}
                            <div>
                                <h3 className="text-lg font-semibold mb-3 text-gray-900">Contact Information</h3>
                                <div className="grid grid-cols-2 gap-4">
                                    <div>
                                        <Label>Name</Label>
                                        <p className="font-medium">{selectedSubmission.name}</p>
                                    </div>
                                    <div>
                                        <Label>Email</Label>
                                        <p className="font-medium">{selectedSubmission.email}</p>
                                    </div>
                                    {selectedSubmission.phone && (
                                        <div>
                                            <Label>Phone</Label>
                                            <p className="font-medium">{selectedSubmission.phone}</p>
                                        </div>
                                    )}
                                    {selectedSubmission.company_name && (
                                        <div>
                                            <Label>Company Name</Label>
                                            <p className="font-medium">{selectedSubmission.company_name}</p>
                                        </div>
                                    )}
                                    {selectedSubmission.exhibition_name && (
                                        <div>
                                            <Label>Exhibition Name</Label>
                                            <p className="font-medium">{selectedSubmission.exhibition_name}</p>
                                        </div>
                                    )}
                                    {selectedSubmission.budget && (
                                        <div>
                                            <Label>Budget</Label>
                                            <p className="font-medium">{selectedSubmission.budget}</p>
                                        </div>
                                    )}
                                    {selectedSubmission.event && (
                                        <div>
                                            <Label>Related Event</Label>
                                            <p className="font-medium">{selectedSubmission.event.title}</p>
                                        </div>
                                    )}
                                    <div>
                                        <Label>Form Type</Label>
                                        <p className="font-medium capitalize">{selectedSubmission.form_type}</p>
                                    </div>
                                    <div>
                                        <Label>Status</Label>
                                        <div className="mt-1">
                                            {getStatusBadge(selectedSubmission.status)}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {/* Message */}
                            <div>
                                <h3 className="text-lg font-semibold mb-3 text-gray-900">Message</h3>
                                <div className="p-4 bg-gray-50 rounded-lg border">
                                    <p className="whitespace-pre-wrap text-gray-700">
                                        {selectedSubmission.message}
                                    </p>
                                </div>
                            </div>

                            {/* Attachment Information */}
                            {selectedSubmission.attachment_url && (
                                <div>
                                    <h3 className="text-lg font-semibold mb-3 text-gray-900">Attachment</h3>
                                    <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                                        <div className="flex items-center justify-between">
                                            <div className="flex items-center gap-3">
                                                <div className="p-2 bg-blue-100 rounded-lg">
                                                    <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                                    </svg>
                                                </div>
                                                <div>
                                                    <p className="font-medium text-gray-900">
                                                        {selectedSubmission.attachment_filename || 'Attachment'}
                                                    </p>
                                                    {selectedSubmission.attachment_size && (
                                                        <p className="text-sm text-gray-600">
                                                            {(selectedSubmission.attachment_size / 1024 / 1024).toFixed(2)} MB
                                                        </p>
                                                    )}
                                                    {selectedSubmission.attachment_type && (
                                                        <p className="text-xs text-gray-500">
                                                            {selectedSubmission.attachment_type}
                                                        </p>
                                                    )}
                                                </div>
                                            </div>
                                            <Button
                                                onClick={() => handleDownloadAttachment(selectedSubmission)}
                                                className="bg-blue-600 hover:bg-blue-700 text-white"
                                                size="sm"
                                            >
                                                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                                </svg>
                                                Download
                                            </Button>
                                        </div>
                                    </div>
                                </div>
                            )}

                            {/* Submission Metadata */}
                            <div>
                                <h3 className="text-lg font-semibold mb-3 text-gray-900">Submission Details</h3>
                                <div className="grid grid-cols-2 gap-4 text-sm">
                                    <div>
                                        <Label>Submitted</Label>
                                        <p className="font-medium">{formatDate(selectedSubmission.created_at)}</p>
                                    </div>
                                    {selectedSubmission.updated_at !== selectedSubmission.created_at && (
                                        <div>
                                            <Label>Updated</Label>
                                            <p className="font-medium">{formatDate(selectedSubmission.updated_at)}</p>
                                        </div>
                                    )}
                                    {selectedSubmission.referrer && (
                                        <div className="col-span-2">
                                            <Label>Referrer URL</Label>
                                            <p className="font-mono text-xs bg-gray-100 p-2 rounded break-all">
                                                {selectedSubmission.referrer}
                                            </p>
                                        </div>
                                    )}
                                    {selectedSubmission.is_spam && (
                                        <div>
                                            <Label>Spam Score</Label>
                                            <p className="font-medium text-red-600">
                                                {selectedSubmission.spam_score?.toFixed(2) || 'N/A'}
                                            </p>
                                        </div>
                                    )}
                                </div>
                            </div>

                            {/* Admin Notes */}
                            {selectedSubmission.admin_notes && (
                                <div>
                                    <h3 className="text-lg font-semibold mb-3 text-gray-900">Admin Notes</h3>
                                    <div className="p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                                        <p className="whitespace-pre-wrap text-gray-700">
                                            {selectedSubmission.admin_notes}
                                        </p>
                                    </div>
                                </div>
                            )}
                        </div>
                    )}
                    
                    <DialogFooter>
                        <Button variant="outline" onClick={() => setShowViewDialog(false)}>
                            Close
                        </Button>
                        {selectedSubmission && (
                            <Button onClick={() => {
                                setShowViewDialog(false);
                                handleReplySubmission(selectedSubmission);
                            }}>
                                <Reply className="h-4 w-4 mr-2" />
                                Reply
                            </Button>
                        )}
                    </DialogFooter>
                </DialogContent>
            </Dialog>

            {/* Reply Dialog */}
            <Dialog open={showReplyDialog} onOpenChange={setShowReplyDialog}>
                <DialogContent className="max-w-2xl">
                    <DialogHeader>
                        <DialogTitle>Reply to Submission</DialogTitle>
                        <DialogDescription>
                            Send a reply to {selectedSubmission?.name} ({selectedSubmission?.email})
                        </DialogDescription>
                    </DialogHeader>
                    
                    <div className="space-y-4">
                        <div>
                            <Label htmlFor="reply-message">Reply Message</Label>
                            <Textarea
                                id="reply-message"
                                value={replyMessage}
                                onChange={(e) => setReplyMessage(e.target.value)}
                                placeholder="Type your reply message here..."
                                rows={6}
                                className="mt-1"
                            />
                        </div>
                    </div>
                    
                    <DialogFooter>
                        <Button 
                            variant="outline" 
                            onClick={() => setShowReplyDialog(false)}
                            disabled={actionLoading}
                        >
                            Cancel
                        </Button>
                        <Button 
                            onClick={handleSendReply}
                            disabled={actionLoading || !replyMessage.trim()}
                        >
                            {actionLoading ? (
                                <>
                                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                                    Sending...
                                </>
                            ) : (
                                <>
                                    <Reply className="h-4 w-4 mr-2" />
                                    Send Reply
                                </>
                            )}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    );
}
